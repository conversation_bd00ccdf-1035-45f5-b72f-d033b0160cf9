/* 截屏窗口样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  cursor: crosshair;
  user-select: none;
  font-family: "Microsoft Yahei", "Microsoft Yahei", Times, serif;
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

/* 截屏覆盖层 */
.screenshot-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  cursor: crosshair;
}

/* 四个遮罩层，用于创建"挖空"效果 */
.mask-top,
.mask-bottom,
.mask-left,
.mask-right {
  position: absolute;
  background: rgba(0, 0, 0, 0.3);
  pointer-events: none;
}

.mask-top {
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}

.mask-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
}

.mask-left {
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
}

.mask-right {
  top: 0;
  right: 0;
  width: 0;
  height: 100%;
}

/* 选择区域 */
.selection-area {
  position: absolute;
  border: 2px solid #007acc;
  background: transparent;
  display: none;
  pointer-events: auto;
}

.selection-area::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  pointer-events: none;
}

/* 调整节点 */
.resize-handle {
  position: absolute;
  background: #007acc;
  border: 2px solid #ffffff;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  cursor: pointer;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.selection-area:hover .resize-handle,
.selection-area.resizing .resize-handle {
  opacity: 1;
}

/* 角节点 */
.resize-handle.nw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.resize-handle.ne {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.resize-handle.sw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.resize-handle.se {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

/* 边节点 */
.resize-handle.n {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.resize-handle.s {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.resize-handle.w {
  top: 50%;
  left: -6px;
  transform: translateY(-50%);
  cursor: w-resize;
}

.resize-handle.e {
  top: 50%;
  right: -6px;
  transform: translateY(-50%);
  cursor: e-resize;
}

/* 工具栏 */
.toolbar {
  position: absolute;
  background: rgba(40, 40, 40, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px;
  display: none;
  z-index: 1001;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.toolbar-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.toolbar-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.toolbar-button i {
  font-size: 16px;
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 4px;
}

/* 尺寸信息 */
.size-info {
  position: absolute;
  background: rgba(40, 40, 40, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  display: none;
  z-index: 1001;
  pointer-events: none;
}

/* 提示信息 */
.hint-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 1001;
  pointer-events: none;
}

.hint-text {
  font-size: 18px;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.hint-shortcuts {
  display: flex;
  gap: 16px;
  justify-content: center;
  font-size: 14px;
  opacity: 0.8;
}

.hint-shortcuts span {
  background: rgba(40, 40, 40, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  backdrop-filter: blur(5px);
}

/* 加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #007acc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 隐藏状态 */
.hidden {
  display: none !important;
}

/* 选择中状态 */
.selecting {
  cursor: crosshair;
}

.selecting .hint-info {
  display: none;
}