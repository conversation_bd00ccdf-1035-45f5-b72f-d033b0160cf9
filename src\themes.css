/* =================== 通用主题系统 =================== */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #4a89dc;
  --primary-hover: #357abd;

  /* 窗口透明度 */
  --window-opacity: 0.85;

  /* 动画时长 */
  --transition-duration: 0.2s;

  /* 圆角 */
  --border-radius: 6px;
  --border-radius-small: 4px;

  /* 阴影 */
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* =================== 默认主题（跟随系统） =================== */

/* 默认亮色模式 */
body {
  /* background-color: rgba(255, 255, 255, var(--window-opacity)); */
  background: transparent;
  color: #222;
  transition: all var(--transition-duration) ease;
}

/* 系统暗色模式 */
@media (prefers-color-scheme: dark) {
  body {
    background-color: rgba(30, 30, 30, var(--window-opacity));
    color: #e0e0e0;
    border-color: rgba(255, 255, 255, 0.1);
  }

  /* 标题栏 */
  .title-bar {
    background-color: rgba(40, 40, 40, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
  }

  .title {
    color: #e0e0e0;
  }

  /* 控制按钮 */
  .control-button {
    color: #ccc;
  }

  .control-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* 标签导航 */
  .tab-navigation {
    background: rgba(40, 40, 40, 0.9);
  }

  .tab-button {
    color: #ccc;
  }

  .tab-button:hover {
    color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.1);
  }

  .tab-button.active {
    color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* 输入框 */
  #search-input,
  #quick-texts-search {
    background-color: #333;
    border-color: #444;
    color: #e0e0e0;
  }

  #search-input:focus,
  #quick-texts-search:focus {
    border-color: var(--primary-color);
  }

  /* 内容项 */
  .clipboard-item,
  .quick-text-item {
    background-color: rgba(50, 50, 50, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
  }

  .clipboard-item:hover,
  .quick-text-item:hover {
    background-color: rgba(60, 60, 60, 0.9);
    border-color: var(--primary-color);
  }

  .clipboard-item.active,
  .quick-text-item.active {
    border-color: var(--primary-color);
    background-color: rgba(74, 137, 220, 0.2);
  }

  /* 模态框 */
  .modal-content {
    background: rgba(40, 40, 40, 0.95);
    color: #e0e0e0;
  }

  .modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .modal-footer {
    border-top-color: rgba(255, 255, 255, 0.1);
    background: rgba(30, 30, 30, 0.5);
  }

  /* 表单元素 */
  .form-group input,
  .form-group textarea,
  .setting-select,
  .group-select,
  .icon-select {
    background-color: #333;
    border-color: #444;
    color: #e0e0e0;
  }

  .form-group input:focus,
  .form-group textarea:focus,
  .setting-select:focus,
  .group-select:focus,
  .icon-select:focus {
    border-color: var(--primary-color);
  }

  /* 按钮 */
  .action-button {
    background: rgba(68, 68, 68, 0.9);
    color: #ccc;
  }

  .action-button:hover {
    background: rgba(80, 80, 80, 0.9);
    color: #fff;
  }

  /* 开关 */
  .toggle-label {
    background-color: #444;
  }

  .toggle-label:hover {
    background-color: #555;
  }

  .toggle-switch:checked+.toggle-label {
    background-color: var(--primary-color);
  }

  /* 设置页面样式 */
  .settings-container {
    background-color: rgba(30, 30, 30, var(--window-opacity));
    color: #e0e0e0;
  }

  .settings-header {
    background-color: rgba(40, 40, 40, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .header-left h1 {
    color: #e0e0e0;
  }

  .settings-sidebar {
    background-color: rgba(40, 40, 40, 0.95);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* 控制按钮 */
  .control-button {
    color: #ccc;
  }

  .minimize-btn:hover {
    background: rgba(33, 150, 243, 0.2);
    color: #2196f3;
  }

  .maximize-btn:hover {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
  }

  .close-button:hover {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
  }

  .settings-nav {
    background-color: rgba(40, 40, 40, 0.95);
    border-right-color: rgba(255, 255, 255, 0.1);
  }

  .nav-item {
    color: #ccc;
  }

  .nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
  }

  .nav-item.active {
    background-color: var(--primary-color);
    color: #fff;
  }

  .settings-content {
    background-color: rgba(50, 50, 50, 0.9);
  }

  .setting-section h2 {
    color: #e0e0e0;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .setting-title {
    color: #e0e0e0;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .setting-option {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .setting-option:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .setting-label {
    color: #ccc;
  }

  .setting-description {
    color: #888;
  }

  /* 主题选择器 */
  .theme-selector {
    background-color: rgba(60, 60, 60, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .theme-option {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .theme-option:hover {
    border-color: var(--primary-color);
  }

  .theme-option.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.3);
  }

  .theme-label {
    color: #ccc;
  }

  /* 设置区域 */
  .settings-main {
    background-color: rgba(50, 50, 50, 0.9);
  }

  .settings-section {
    background-color: transparent;
  }

  .section-header h2 {
    color: #e0e0e0;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .section-header p {
    color: #888;
  }

  .settings-group {
    background-color: rgba(60, 60, 60, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .setting-item {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .setting-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .setting-info .setting-label {
    color: #e0e0e0;
  }

  .setting-info .setting-description {
    color: #888;
  }

  /* 滑块控件 */
  .slider-container {
    color: #ccc;
  }

  .slider-value {
    color: #e0e0e0;
  }

  /* 开关控件 */
  .toggle-switch .toggle-slider {
    background-color: rgba(189, 189, 189, 0.5);
  }

  .toggle-switch input:checked+.toggle-slider {
    background-color: var(--primary-color);
  }

  /* 设置页面的toggle-slider样式 - 默认主题 */
  .settings-modal .toggle-slider {
    background-color: rgba(189, 189, 189, 0.7) !important;
  }

  .settings-modal input:checked+.toggle-slider {
    background-color: var(--primary-color) !important;
  }

  /* 内容筛选器 */
  .content-filter {
    background-color: #333;
    border-color: #444;
    color: #e0e0e0;
  }

  /* 索引指示器 */
  .clipboard-index {
    background-color: rgba(68, 68, 68, 0.8);
    color: #ccc;
  }

  /* 页脚 */
  .footer {
    border-top-color: rgba(255, 255, 255, 0.1);
    color: #888;
  }

  /* 空状态提示 */
  .empty-hint {
    color: #888;
  }

  /* 拖拽样式 */
  .sortable-ghost {
    background-color: #2a3a4a !important;
    border-color: var(--primary-color) !important;
  }

  /* 分组侧边栏 */
  .groups-sidebar {
    background: rgba(40, 40, 40, 0.95);
    border-left-color: rgba(255, 255, 255, 0.1);
  }

  .groups-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .groups-header h3 {
    color: #ccc;
  }

  .add-group-btn {
    color: #ccc;
  }

  .add-group-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
  }

  .group-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .group-action-btn {
    background: rgba(40, 40, 40, 0.9);
    color: #ccc;
  }

  .group-action-btn:hover {
    background: #333;
    color: #fff;
  }

  .sidebar-trigger:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* 右键菜单 */
  .context-menu {
    background: #2d2d2d !important;
    border-color: #404040 !important;
    color: #e0e0e0;
  }

  .context-menu-item {
    border-bottom-color: #404040;
  }

  .context-menu-item:hover {
    background-color: #404040 !important;
  }

  /* 占位符 */
  ::placeholder,
  ::-webkit-input-placeholder,
  :-moz-placeholder,
  ::-moz-placeholder,
  :-ms-input-placeholder {
    color: #888;
    opacity: 1;
  }
}

/* =================== 亮色主题 =================== */
body.theme-light {
  background-color: #ffffff;
  color: #222;
}

.theme-light .title-bar {
  background-color: #f0f0f0;
  border-bottom-color: #e0e0e0;
  color: #666;
}

.theme-light .title {
  color: #666;
}

.theme-light .tab-navigation {
  background: #f8f9fa;
}

.theme-light .tab-button {
  color: #666;
}

.theme-light .tab-button:hover {
  color: var(--primary-color);
  background-color: rgba(74, 137, 220, 0.08);
}

.theme-light .tab-button.active {
  color: var(--primary-color);
  background-color: white;
  box-shadow: var(--shadow-light);
}

.theme-light .clipboard-item,
.theme-light .quick-text-item {
  background-color: white;
  border-color: #e8e9ea;
  color: #333;
}

.theme-light .clipboard-item:hover,
.theme-light .quick-text-item:hover {
  background-color: #f8f9fa;
  border-color: var(--primary-color);
}

.theme-light .clipboard-item.active,
.theme-light .quick-text-item.active {
  border-color: var(--primary-color);
  background-color: #f0f7ff;
}

.theme-light #search-input,
.theme-light #quick-texts-search {
  background-color: #f8f9fa;
  border-color: #e8e9ea;
  color: #333;
}

.theme-light #search-input:focus,
.theme-light #quick-texts-search:focus {
  border-color: var(--primary-color);
  background-color: white;
}

.theme-light .modal-content {
  background: white;
  color: #333;
}

.theme-light .modal-header {
  border-bottom-color: #eee;
}

.theme-light .modal-header h3 {
  color: #666;
}

.theme-light .modal-footer {
  border-top-color: #eee;
  background: #f9f9f9;
}

.theme-light .setting-title {
  color: #333;
  border-bottom-color: #eee;
}

.theme-light .setting-option:hover {
  background-color: #f8f9fa;
}

.theme-light .setting-label {
  color: #555;
}

.theme-light .setting-select,
.theme-light .group-select,
.theme-light .icon-select {
  background-color: white;
  border-color: #e0e0e0;
  color: #222;
}

.theme-light .quick-text-title {
  color: #2c3e50;
}

.theme-light .quick-text-content {
  color: #6c757d;
}

.theme-light .action-button {
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  box-shadow: var(--shadow-light);
}

.theme-light .action-button:hover {
  background: rgba(255, 255, 255, 1);
  color: #333;
}

.theme-light .footer {
  border-top-color: #eee;
  color: #999;
}

.theme-light .content-filter {
  background-color: #f8f9fa;
  border-color: #e8e9ea;
  color: #333;
}

.theme-light .form-group label {
  color: #222;
}

.theme-light .form-group input,
.theme-light .form-group textarea {
  background-color: white;
  border-color: #e0e0e0;
  color: #222;
}

.theme-light .toggle-label {
  background-color: white;
}

.theme-light .clipboard-index {
  background-color: #f0f0f0;
  color: #666;
}

/* 亮色主题设置页面样式 */
.theme-light .settings-container {
  background-color: #ffffff;
  color: #333;
}

.theme-light .settings-header {
  background-color: white;
  border-bottom: 1px solid #e9ecef;
}

.theme-light .header-left h1 {
  color: #333;
}

.theme-light .settings-sidebar {
  background-color: white;
  border-right: 1px solid #e9ecef;
}

.theme-light .control-button {
  color: #666;
}

.theme-light .minimize-btn:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.theme-light .maximize-btn:hover {
  background: #e8f5e8;
  color: #388e3c;
}

.theme-light .close-button:hover {
  background: #ffebee;
  color: #d32f2f;
}

.theme-light .settings-nav {
  background-color: #f8f9fa;
  border-right-color: #e8e9ea;
}

.theme-light .nav-item {
  color: #666;
}

.theme-light .nav-item:hover {
  background-color: rgba(74, 137, 220, 0.08);
  color: var(--primary-color);
}

.theme-light .nav-item.active {
  background-color: var(--primary-color);
  color: #fff;
}

.theme-light .settings-content {
  background-color: #ffffff;
}

.theme-light .setting-section h2 {
  color: #333;
  border-bottom-color: #eee;
}

.theme-light .setting-title {
  color: #333;
  border-bottom-color: #eee;
}

.theme-light .setting-option {
  border-bottom-color: #f0f0f0;
}

.theme-light .setting-option:hover {
  background-color: #f8f9fa;
}

.theme-light .setting-label {
  color: #555;
}

.theme-light .setting-description {
  color: #777;
}

.theme-light .theme-selector {
  background-color: #ffffff;
  border-color: #e8e9ea;
}

.theme-light .theme-option {
  border-color: #e8e9ea;
}

.theme-light .theme-option:hover {
  border-color: var(--primary-color);
}

.theme-light .theme-option.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.2);
}

.theme-light .theme-label {
  color: #666;
}

.theme-light .context-menu {
  background: #ffffff !important;
  border-color: #e8e9ea !important;
  color: #333;
}

.theme-light .context-menu-item {
  border-bottom-color: #e8e9ea;
}

.theme-light .groups-sidebar {
  background: #ffffff;
  border-left-color: #e8e9ea;
}

.theme-light .group-action-btn {
  background: rgba(40, 40, 40, 0.3);
  color: #ffffff;
}

.theme-light .group-action-btn:hover {
  background: #333;
  color: #fff;
}

.theme-light .group-item:hover {
  background: rgba(74, 137, 220, 0.1);
}

.theme-light .context-menu-item:hover {
  background-color: #e8e9ea !important;
}

/* 亮色主题设置区域 */
.theme-light .settings-main {
  background-color: #ffffff;
}

.theme-light .groups-header * {
  color: #666;
}

.theme-light .settings-section {
  background-color: transparent;
}

.theme-light .section-header h2 {
  color: #333;
  border-bottom-color: #eee;
}

.theme-light .section-header p {
  color: #777;
}

.theme-light .settings-group {
  background-color: #ffffff;
  border-color: #e8e9ea;
}

.theme-light .setting-item {
  border-bottom-color: #f0f0f0;
}

.theme-light .setting-item:hover {
  background-color: #f8f9fa;
}

.theme-light .setting-info .setting-label {
  color: #333;
}

.theme-light .setting-info .setting-description {
  color: #777;
}

.theme-light .slider-container {
  color: #666;
}

.theme-light .slider-value {
  color: #333;
}

.theme-light .toggle-switch .toggle-slider {
  background-color: #cdcdcd !important;
}

.theme-light .toggle-switch input:checked+.toggle-slider {
  background-color: #1976d2 !important;
}

/* 设置页面的toggle-slider样式 - 亮色主题 */
.theme-light .settings-modal .toggle-slider {
  background-color: #e0e0e0 !important;
}

.theme-light .settings-modal input:checked+.toggle-slider {
  background-color: #1976d2 !important;
}

.theme-light .sound-input,
.sound-browse-btn,
.sound-test-btn {
  background: transparent !important;
  color: #333 !important;
}

/* 亮色主题占位符 */
.theme-light ::placeholder,
.theme-light ::-webkit-input-placeholder,
.theme-light :-moz-placeholder,
.theme-light ::-moz-placeholder,
.theme-light :-ms-input-placeholder {
  color: #999;
  opacity: 1;
}

/* =================== 暗色主题 =================== */
body.theme-dark {
  background-color: rgba(30, 30, 30, var(--window-opacity));
  color: #e0e0e0;
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .title-bar {
  background-color: rgba(40, 40, 40, 0.95);
  border-bottom-color: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
}

.theme-dark .title {
  color: #e0e0e0;
}

.theme-dark .control-button {
  color: #ccc;
}

.theme-dark .control-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .tab-navigation {
  background: rgba(40, 40, 40, 0.9);
}

.theme-dark .tab-button {
  color: #ccc;
}

.theme-dark .tab-button:hover {
  color: var(--primary-color);
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .tab-button.active {
  color: var(--primary-color);
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-dark #search-input,
.theme-dark #quick-texts-search {
  background-color: #333;
  border-color: #444;
  color: #e0e0e0;
}

.theme-dark #search-input:focus,
.theme-dark #quick-texts-search:focus {
  border-color: var(--primary-color);
}

.theme-dark .clipboard-item,
.theme-dark .quick-text-item {
  background-color: rgba(50, 50, 50, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
}

.theme-dark .clipboard-item:hover,
.theme-dark .quick-text-item:hover {
  background-color: rgba(60, 60, 60, 0.9);
  border-color: var(--primary-color);
}

.theme-dark .clipboard-item.active,
.theme-dark .quick-text-item.active {
  border-color: var(--primary-color);
  background-color: rgba(74, 137, 220, 0.2);
}

.theme-dark .modal-content {
  background: rgba(40, 40, 40, 0.95);
  color: #e0e0e0;
}

.theme-dark .modal-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .modal-footer {
  border-top-color: rgba(255, 255, 255, 0.1);
  background: rgba(30, 30, 30, 0.5);
}

.theme-dark .form-group input,
.theme-dark .form-group textarea,
.theme-dark .setting-select,
.theme-dark .group-select,
.theme-dark .icon-select {
  background-color: #333;
  border-color: #444;
  color: #e0e0e0;
}

.theme-dark .form-group input:focus,
.theme-dark .form-group textarea:focus,
.theme-dark .setting-select:focus,
.theme-dark .group-select:focus,
.theme-dark .icon-select:focus {
  border-color: var(--primary-color);
}

/* 图标网格暗色模式 */
.theme-dark .icon-grid {
  background-color: #333;
  border-color: #444;
}

.theme-dark .icon-option {
  background-color: #2a2a2a;
  border-color: #444;
}

.theme-dark .icon-option:hover {
  background-color: #3a3a3a;
  border-color: var(--primary-color);
}

.theme-dark .icon-option.active {
  background-color: rgba(74, 137, 220, 0.2);
  border-color: var(--primary-color);
}

.theme-dark .icon-option i {
  color: #ccc;
}

.theme-dark .icon-option:hover i,
.theme-dark .icon-option.active i {
  color: var(--primary-color);
}

.theme-dark .action-button {
  background: rgba(68, 68, 68, 0.9);
  color: #ccc;
}

.theme-dark .action-button:hover {
  background: rgba(80, 80, 80, 0.9);
  color: #fff;
}

.theme-dark .toggle-label {
  background-color: #444;
}

.theme-dark .toggle-label:hover {
  background-color: #555;
}

.theme-dark .toggle-switch:checked+.toggle-label {
  background-color: var(--primary-color);
}

.theme-dark .content-filter {
  background-color: #333;
  border-color: #444;
  color: #e0e0e0;
}

.theme-dark .clipboard-index {
  background-color: rgba(68, 68, 68, 0.8);
  color: #ccc;
}

.theme-dark .preset-btn {
  background-color: #323232;
  color: #ccc;
  border: 1px solid #ddd;
}

/* 暗色主题设置页面样式 */
.theme-dark .settings-container {
  background-color: rgba(30, 30, 30, var(--window-opacity));
  color: #e0e0e0;
}

.theme-dark .settings-header {
  background-color: rgba(40, 40, 40, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .header-left h1 {
  color: #e0e0e0;
}

.theme-dark .settings-sidebar {
  background-color: rgba(40, 40, 40, 0.95);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .control-button {
  color: #ccc;
}

.theme-dark .minimize-btn:hover {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.theme-dark .maximize-btn:hover {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.theme-dark .close-button:hover {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.theme-dark .settings-nav {
  background-color: rgba(40, 40, 40, 0.95);
  border-right-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .nav-item {
  color: #ccc;
}

.theme-dark .nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.theme-dark .nav-item.active {
  background-color: var(--primary-color);
  color: #fff;
}

.theme-dark .settings-content {
  background-color: rgba(50, 50, 50, 0.9);
}

.theme-dark .setting-section h2 {
  color: #e0e0e0;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .setting-title {
  color: #e0e0e0;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .setting-option {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .setting-option:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.theme-dark .setting-label {
  color: #ccc;
}

.theme-dark .setting-description {
  color: #888;
}

.theme-dark .theme-selector {
  background-color: rgba(60, 60, 60, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .theme-option {
  border-color: rgba(255, 255, 255, 0.2);
}

.theme-dark .theme-option:hover {
  border-color: var(--primary-color);
}

.theme-dark .theme-option.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.3);
}

.theme-dark .theme-label {
  color: #ccc;
}

.theme-dark .modal-overlay * {
  color: #ccc;
}

.theme-dark .shortcut-input {
  background: #323232;
  color: #ccc;
}

.theme-dark .shortcut-clear {
  color: #ccc;
}

.theme-dark .sound-input,
.sound-browse-btn,
.sound-test-btn {
  background: transparent !important;
  color: #ccc !important;
}

/* .theme-dark #toggle-shortcut {
  background: #3A3A3A !important;
  color: #ccc;
} */

/* 暗色主题设置区域 */
.theme-dark .settings-main {
  background-color: rgba(50, 50, 50, 0.9);
}

.theme-dark .settings-section {
  background-color: transparent;
}

.theme-dark .section-header h2 {
  color: #e0e0e0;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .section-header p {
  color: #888;
}

.theme-dark .settings-group {
  background-color: rgba(60, 60, 60, 0.8);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .setting-item {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .setting-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.theme-dark .setting-info .setting-label {
  color: #e0e0e0;
}

.theme-dark .setting-info .setting-description {
  color: #888;
}

.theme-dark .slider-container {
  color: #ccc;
}

.theme-dark .slider-value {
  color: #e0e0e0;
}

.theme-dark .toggle-switch .toggle-slider {
  background-color: #444 !important;
}

.theme-dark .toggle-switch input:checked+.toggle-slider {
  background-color: var(--primary-color) !important;
}

.theme-dark .file-item {
  background-color: #313131;
  border: 0.1px solid #7c7c7c;
}

.theme-dark .file-item .file-info * {
  color: #ffffff !important;

}

/* 设置页面的toggle-slider样式 - 暗色主题 */
.theme-dark .settings-modal .toggle-slider {
  background-color: #555 !important;
}

.theme-dark .settings-modal input:checked+.toggle-slider {
  background-color: var(--primary-color) !important;
}

.theme-dark .footer {
  border-top-color: rgba(255, 255, 255, 0.1);
  color: #888;
}

.theme-dark .empty-hint {
  color: #888;
}

.theme-dark .sortable-ghost {
  background-color: #2a3a4a !important;
  border-color: var(--primary-color) !important;
}

.theme-dark .groups-sidebar {
  background: rgba(40, 40, 40, 0.95);
  border-left-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .groups-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .groups-header h3 {
  color: #ccc;
}

.theme-dark .add-group-btn {
  color: #ccc;
}

.theme-dark .add-group-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.theme-dark .group-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.theme-dark .group-action-btn {
  background: rgba(40, 40, 40, 0.9);
  color: #ccc;
}

.theme-dark .group-action-btn:hover {
  background: #333;
  color: #fff;
}

.theme-dark .sidebar-trigger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .context-menu {
  background: #2d2d2d !important;
  border-color: #404040 !important;
  color: #e0e0e0 !important;
}

.theme-dark .context-menu-item {
  color: #e0e0e0 !important;
  border-bottom-color: #404040;
}

/* 最后一项 */
.theme-dark .context-menu-item:last-child {
  color: rgb(255, 77, 79) !important;
}

.theme-dark .context-menu-item:hover {
  background-color: #404040 !important;
}

.theme-dark .about-info * {
  color: #e0e0e0 !important;
}


.theme-dark .app-icon i {
  color: #4a89dc !important;
}

.theme-dark .confirm-dialog {
  background-color: #2d2d2d;
}

/* 暗色主题占位符 */
.theme-dark ::placeholder,
.theme-dark ::-webkit-input-placeholder,
.theme-dark :-moz-placeholder,
.theme-dark ::-moz-placeholder,
.theme-dark :-ms-input-placeholder {
  color: #888;
  opacity: 1;
}

/* =================== 透明主题 =================== */
body.theme-transparent {
  background: rgba(255, 255, 255, calc(var(--window-opacity) * 0.15));
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
}

.theme-transparent .title-bar {
  background-color: rgba(240, 240, 240, 0.3);
  border-bottom-color: rgba(224, 224, 224, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .toggle-label {
  background-color: rgba(204, 204, 204, 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .toggle-label:hover {
  background-color: rgba(204, 204, 204, 0.7);
}

.theme-transparent .toggle-switch:checked+.toggle-label {
  background-color: rgba(74, 137, 220, 0.8);
}

.theme-transparent .toggle-switch:checked+.toggle-label:hover {
  background-color: rgba(74, 137, 220, 0.9);
}

/* .theme-transparent .toggle-slider {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
} */

.theme-transparent .tab-navigation {
  background: rgba(248, 249, 250, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .tab-button.active {
  background-color: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .clipboard-item,
.theme-transparent .quick-text-item {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(238, 238, 238, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .clipboard-item:hover,
.theme-transparent .quick-text-item:hover {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(74, 137, 220, 0.5);
}

.theme-transparent .quick-text-image {
  opacity: 0.95;
}

.theme-transparent .clipboard-actions .action-button {
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .clipboard-actions .action-button:hover {
  background: rgba(255, 255, 255, 0.5);
}

.theme-transparent .clipboard-actions .action-button.add-to-favorites:hover {
  background: rgba(255, 215, 0, 0.8);
  color: #333;
}

.theme-transparent .clipboard-actions .action-button.open-link {
  background: rgba(74, 137, 220, 0.3);
  color: var(--primary-color);
}

.theme-transparent .clipboard-actions .action-button.open-link:hover {
  background: rgba(74, 137, 220, 0.8);
  color: white;
}

.theme-transparent #search-input,
.theme-transparent #quick-texts-search {
  background-color: rgba(248, 249, 250, 0.3);
  border-color: rgba(232, 233, 234, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .content-filter {
  background-color: #868686 !important;
  border-color: rgba(255, 255, 255, 0.2);
  color: #fff !important;
}

.theme-transparent .content-filter:hover {
  background-color: rgba(255, 255, 255, 0.35);
  border-color: rgba(232, 233, 234, 0.4);
}

.theme-transparent .content-filter:focus {
  background-color: rgba(255, 255, 255, 0.4);
  border-color: rgba(74, 137, 220, 0.5);
}

.theme-transparent .modal-content {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-transparent .modal-header {
  border-bottom-color: rgba(238, 238, 238, 0.3);
}

.theme-transparent .modal-footer {
  border-top-color: rgba(238, 238, 238, 0.3);
  background: rgba(249, 249, 249, 0.2);
}

.theme-transparent .groups-sidebar {
  background: rgba(0, 0, 0, 0.3);
  border-left-color: rgba(255, 255, 255, 0.2);
}

.theme-transparent .groups-header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.theme-transparent .groups-header h3 {
  color: rgba(255, 255, 255, 0.9);
}

.theme-transparent .add-group-btn {
  color: rgba(255, 255, 255, 0.8);
}

.theme-transparent .add-group-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.theme-transparent .group-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

.theme-transparent .group-name {
  color: rgba(255, 255, 255, 0.9);
}

.theme-transparent .context-menu {
  background: rgba(84, 84, 84, 0.5) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.theme-transparent .context-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.8) !important;
}

.theme-transparent .group-action-btn {
  background: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.8);
}

.theme-transparent .group-action-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
}

.theme-transparent .sidebar-trigger:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 透明主题设置页面样式 */
.theme-transparent .settings-container {
  background: rgba(255, 255, 255, calc(var(--window-opacity) * 0.15));
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  color: #fff;
}

.theme-transparent .settings-header {
  background: rgba(255, 255, 255, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .header-left h1 {
  color: rgba(255, 255, 255, 0.9);
}

.theme-transparent .settings-sidebar {
  background: rgba(0, 0, 0, 0.3);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .control-button {
  color: rgba(255, 255, 255, 0.8);
}

.theme-transparent .minimize-btn:hover {
  background: rgba(33, 150, 243, 0.3);
  color: #64b5f6;
}

.theme-transparent .maximize-btn:hover {
  background: rgba(76, 175, 80, 0.3);
  color: #81c784;
}

.theme-transparent .close-button:hover {
  background: rgba(244, 67, 54, 0.3);
  color: #e57373;
}

.theme-transparent .settings-nav {
  background: rgba(0, 0, 0, 0.3);
  border-right-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .nav-item {
  color: rgba(255, 255, 255, 0.8);
}

.theme-transparent .nav-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.theme-transparent .nav-item.active {
  background-color: rgba(74, 137, 220, 0.8);
  color: #fff;
}

.theme-transparent .settings-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.theme-transparent .setting-section h2 {
  color: rgba(255, 255, 255, 0.9);
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.theme-transparent .setting-title {
  color: rgba(255, 255, 255, 0.9);
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.theme-transparent .setting-option {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-transparent .setting-option:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-transparent .setting-label {
  color: rgba(255, 255, 255, 0.9);
}

.theme-transparent .setting-description {
  color: rgba(255, 255, 255, 0.7);
}

.theme-transparent .theme-selector {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .theme-option {
  border-color: rgba(255, 255, 255, 0.3);
}

.theme-transparent .theme-option:hover {
  border-color: rgba(74, 137, 220, 0.8);
}

.theme-transparent .theme-option.active {
  border-color: rgba(74, 137, 220, 0.8);
  box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.4);
}

.theme-transparent .theme-label {
  color: rgba(255, 255, 255, 0.9);
}

/* 透明主题设置区域 */
.theme-transparent .settings-main {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.theme-transparent .settings-section {
  background-color: transparent;
}

.theme-transparent .section-header h2 {
  color: rgba(255, 255, 255, 0.95);
  border-bottom-color: rgba(255, 255, 255, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.theme-transparent .section-header p {
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.theme-transparent .settings-group {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-transparent .setting-item {
  border-bottom-color: rgba(255, 255, 255, 0.15);
}

.theme-transparent .setting-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-transparent .setting-info .setting-label {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 600;
}

.theme-transparent .setting-info .setting-description {
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.theme-transparent .slider-container {
  color: rgba(255, 255, 255, 0.9);
}

.theme-transparent .slider-value {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 600;
}

.theme-transparent .toggle-switch .toggle-slider {
  background-color: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.theme-transparent .toggle-switch input:checked+.toggle-slider {
  background-color: rgba(74, 137, 220, 0.8) !important;
}

/* 设置页面的toggle-slider样式 - 透明主题 */
.theme-transparent .settings-modal .toggle-slider {
  background-color: rgba(255, 255, 255, 0.4) !important;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.theme-transparent .settings-modal input:checked+.toggle-slider {
  background-color: rgba(74, 137, 220, 0.8) !important;
}

/* 透明主题占位符 */
.theme-transparent ::placeholder,
.theme-transparent ::-webkit-input-placeholder,
.theme-transparent :-moz-placeholder,
.theme-transparent ::-moz-placeholder,
.theme-transparent :-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
  opacity: 1;
}