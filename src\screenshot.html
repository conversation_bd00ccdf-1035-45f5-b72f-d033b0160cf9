<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <link rel="stylesheet" href="./assets/icons/icons-webfont/dist/tabler-icons.min.css">
  <!-- <link rel="stylesheet" href="themes.css"> -->
  <link rel="stylesheet" href="screenshot.css" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>截屏选择</title>
  <script type="module" src="screenshot.js" defer></script>
</head>

<body>
  <!-- 截屏覆盖层 -->
  <div class="screenshot-overlay" id="screenshot-overlay">
    <!-- 四个遮罩层，用于创建"挖空"效果 -->
    <div class="mask-top" id="mask-top"></div>
    <div class="mask-bottom" id="mask-bottom"></div>
    <div class="mask-left" id="mask-left"></div>
    <div class="mask-right" id="mask-right"></div>

    <!-- 选择区域 -->
    <div class="selection-area" id="selection-area">
      <!-- 8个调整节点 -->
      <!-- 四个角 -->
      <div class="resize-handle nw" data-direction="nw"></div>
      <div class="resize-handle ne" data-direction="ne"></div>
      <div class="resize-handle sw" data-direction="sw"></div>
      <div class="resize-handle se" data-direction="se"></div>
      <!-- 四个边的中点 -->
      <div class="resize-handle n" data-direction="n"></div>
      <div class="resize-handle s" data-direction="s"></div>
      <div class="resize-handle w" data-direction="w"></div>
      <div class="resize-handle e" data-direction="e"></div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar" id="toolbar">
      <div class="toolbar-content">
        <button class="toolbar-button" id="confirm-button" title="确认截屏">
          <i class="ti ti-check"></i>
          <span>确认</span>
        </button>
        <button class="toolbar-button" id="cancel-button" title="取消截屏">
          <i class="ti ti-x"></i>
          <span>取消</span>
        </button>
        <!-- <div class="toolbar-separator"></div> -->
        <button class="toolbar-button" id="fullscreen-button" style="display: none;" title="全屏截屏">
          <i class="ti ti-maximize"></i>
          <span>全屏</span>
        </button>
      </div>
    </div>

    <!-- 尺寸信息显示 -->
    <div class="size-info" id="size-info">
      <span id="size-text">0 × 0</span>
    </div>

    <!-- 提示信息 -->
    <div class="hint-info" id="hint-info">
      <div class="hint-text">拖拽鼠标选择截屏区域</div>
      <!-- <div class="hint-shortcuts">
        <span>ESC 取消</span>
        <span>Enter 确认</span>
        <span>F11 全屏</span>
      </div> -->
    </div>
  </div>

  <!-- 加载提示 -->
  <div class="loading-overlay" id="loading-overlay">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在截屏...</div>
    </div>
  </div>
</body>

</html>