<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/x-icon"
    href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><rect x='8' y='2' width='8' height='4' rx='1' ry='1'/><path d='M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2'/></rect></svg>">
  <link rel="stylesheet" href="./assets/icons/icons-webfont/dist/tabler-icons.min.css">
  <link rel="stylesheet" href="styles.css" />
  <link rel="stylesheet" href="themes.css" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>快速剪贴板</title>
  <script type="module" src="/main.js" defer></script>
</head>

<body>
  <div class="title-bar">
    <div class="title"><img src="./assets/icon1024.png" style="width: 18px; height: 18px;" alt="">QuickClipboard</div>
    <div class="controls">
      <!-- 截屏按钮 -->
      <button id="screenshot-button" class="control-button" title="截屏"><i class="ti ti-screenshot"></i></button>

      <!-- 一次性粘贴开关 -->
      <div class="one-time-paste-toggle" title="开启后粘贴常用文本会自动删除该项（仅适用于常用文本）">
        <input type="checkbox" id="one-time-paste-switch" class="toggle-switch">
        <label for="one-time-paste-switch" class="toggle-label">
          <span class="toggle-slider"></span>
        </label>
      </div>

      <!-- AI翻译开关 -->
      <div class="ai-translation-toggle" title="开启AI自动翻译功能">
        <input type="checkbox" id="ai-translation-switch" class="toggle-switch">
        <label for="ai-translation-switch" class="toggle-label">
          <span class="toggle-slider"></span>
          <i class="ti ti-language toggle-icon"></i>
        </label>
      </div>

      <button id="settings-button" class="control-button" title="设置"><i class="ti ti-settings"></i></button>

      <button id="pin-button" class="control-button" title="固定"><i class="ti ti-pin"></i></button>
    </div>
  </div>

  <main class="container">
    <!-- AI翻译进度指示器 -->
    <div id="ai-translation-indicator" class="ai-translation-indicator" style="display: none;">
      <div class="indicator-content">
        <i class="ti ti-language indicator-icon"></i>
        <span class="indicator-text">正在翻译...</span>
        <div class="indicator-spinner"></div>
        <button class="indicator-cancel-btn" id="cancel-translation-btn" title="取消翻译 (Ctrl+Shift+Esc)">
          <i class="ti ti-x"></i>
        </button>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="tab-navigation">
      <button class="tab-button active" data-tab="clipboard">剪贴板历史</button>
      <button class="tab-button" data-tab="quick-texts">常用</button>
    </div>

    <!-- 剪贴板历史标签页 -->
    <div class="tab-content active" id="clipboard-tab">
      <div class="tab-main-content">
        <div class="search-container">
          <input type="search" id="search-input" placeholder="搜索剪贴板内容..." autocomplete="off" />
          <select id="content-filter" class="content-filter">
            <option value="all">全部</option>
            <option value="text">文本</option>
            <option value="image">图片</option>
            <option value="files">文件</option>
            <option value="link">链接</option>
          </select>
        </div>

        <div class="clipboard-list" id="clipboard-list">
          <!-- 剪贴板项目将通过JavaScript动态添加 -->
        </div>
      </div>

      <!-- 侧边栏触发区域 -->
      <div class="sidebar-trigger"></div>

      <!-- 分组侧边栏 -->
      <div class="groups-sidebar" id="clipboard-groups-sidebar">
        <div class="groups-header">
          <h3>分组</h3>
          <button class="add-group-btn" id="add-clipboard-group-btn" title="新增分组">
            <i class="ti ti-plus"></i>
          </button>
        </div>
        <div class="groups-list" id="clipboard-groups-list">
          <!-- 分组列表将通过JavaScript动态添加 -->
        </div>
      </div>
    </div>

    <!-- 常用标签页 -->
    <div class="tab-content" id="quick-texts-tab">
      <div class="tab-main-content">
        <div class="quick-texts-header">
          <div class="search-container">
            <input type="search" id="quick-texts-search" placeholder="搜索常用..." autocomplete="off" />
            <select id="quick-texts-filter" class="content-filter">
              <option value="all">全部</option>
              <option value="text">文本</option>
              <option value="image">图片</option>
              <option value="files">文件</option>
              <option value="link">链接</option>
            </select>
          </div>
          <button class="add-button" id="add-quick-text-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 5v14M5 12h14" />
            </svg>
            添加
          </button>
        </div>

        <div class="quick-texts-list" id="quick-texts-list">
          <!-- 常用项目将通过JavaScript动态添加 -->
        </div>
      </div>

      <!-- 侧边栏触发区域 -->
      <div class="sidebar-trigger"></div>

      <!-- 分组侧边栏 -->
      <div class="groups-sidebar" id="quick-texts-groups-sidebar">
        <div class="groups-header">
          <h3>分组</h3>
          <button class="add-group-btn" id="add-quick-texts-group-btn" title="新增分组">
            <i class="ti ti-plus"></i>
          </button>
        </div>
        <div class="groups-list" id="quick-texts-groups-list">
          <!-- 分组列表将通过JavaScript动态添加 -->
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="shortcuts-info">
        <span id="toggle-shortcut-display">Win+V: 显示/隐藏</span>
        <span>Ctrl+数字: 粘贴对应历史</span>
      </div>
    </div>
  </main>

  <!-- 常用编辑模态框 -->
  <div class="modal-overlay" id="quick-text-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modal-title">添加常用</h3>
        <button class="modal-close" id="modal-close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="quick-text-title">标题</label>
          <input type="text" id="quick-text-title" placeholder="请输入标题..." autocomplete="off" />
        </div>
        <div class="form-group">
          <label for="quick-text-content">内容</label>
          <textarea id="quick-text-content" class="quick-text-content" placeholder="请输入文本内容..." rows="6"
            autocomplete="off"></textarea>
        </div>
        <div class="form-group">
          <label for="quick-text-group">分组</label>
          <select id="quick-text-group" class="group-select">
            <option value="all">全部</option>
            <!-- 分组选项将通过JavaScript动态添加 -->
          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" id="modal-cancel-btn">取消</button>
        <button class="btn btn-primary" id="modal-save-btn">保存</button>
      </div>
    </div>
  </div>

  <!-- 分组管理模态框 -->
  <div class="modal-overlay" id="group-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="group-modal-title">新增分组</h3>
        <button class="modal-close" id="group-modal-close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="group-name">分组名称</label>
          <input type="text" id="group-name" placeholder="请输入分组名称..." autocomplete="off" />
        </div>
        <div class="form-group">
          <label for="group-icon">图标</label>
          <div class="icon-grid-container">
            <div class="icon-grid" id="group-icon-grid">
              <div class="icon-option active" data-icon="ti ti-folder" title="文件夹">
                <i class="ti ti-folder"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-star" title="星标">
                <i class="ti ti-star"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-heart" title="收藏">
                <i class="ti ti-heart"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-bookmark" title="书签">
                <i class="ti ti-bookmark"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-tag" title="标签">
                <i class="ti ti-tag"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-file-text" title="文档">
                <i class="ti ti-file-text"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-code" title="代码">
                <i class="ti ti-code"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-link" title="链接">
                <i class="ti ti-link"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-photo" title="图片">
                <i class="ti ti-photo"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-music" title="音乐">
                <i class="ti ti-music"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-video" title="视频">
                <i class="ti ti-video"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-mail" title="邮件">
                <i class="ti ti-mail"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-phone" title="电话">
                <i class="ti ti-phone"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-calendar" title="日历">
                <i class="ti ti-calendar"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-clock" title="时钟">
                <i class="ti ti-clock"></i>
              </div>
              <div class="icon-option" data-icon="ti ti-settings" title="设置">
                <i class="ti ti-settings"></i>
              </div>
            </div>
            <input type="hidden" id="group-icon" value="ti ti-folder">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" id="group-modal-cancel-btn">取消</button>
        <button class="btn btn-primary" id="group-modal-save-btn">保存</button>
      </div>
    </div>
  </div>

  <!-- 确认删除模态框 -->
  <div class="modal-overlay" id="confirm-modal">
    <div class="modal-content confirm-modal">
      <div class="modal-header">
        <h3 id="confirm-title">确认删除</h3>
      </div>
      <div class="modal-body">
        <p id="confirm-message">确定要删除这个常用吗？</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" id="confirm-cancel-btn">取消</button>
        <button class="btn btn-danger" id="confirm-ok-btn">删除</button>
      </div>
    </div>
  </div>

  <!-- 提示框 -->
  <div class="modal-overlay" id="alert-modal">
    <div class="modal-content alert-modal">
      <div class="modal-header">
        <h3 id="alert-title">提示</h3>
      </div>
      <div class="modal-body">
        <p id="alert-message">这是一个提示消息</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" id="alert-ok-btn">确定</button>
      </div>
    </div>
  </div>

  <!-- 设置页面模态框 -->
  <div class="modal-overlay" id="settings-modal">
    <div class="modal-content settings-modal">
      <div class="modal-header">
        <h3>设置</h3>
        <button class="modal-close" id="settings-close-btn">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="modal-body">
        <!-- 主题设置 -->
        <div class="setting-group">
          <h4 class="setting-title">主题设置</h4>
          <div class="setting-options">
            <label class="setting-option">
              <input type="radio" name="theme" value="system" checked>
              <span class="setting-label">跟随系统</span>
            </label>
            <label class="setting-option">
              <input type="radio" name="theme" value="light">
              <span class="setting-label">亮色主题</span>
            </label>
            <label class="setting-option">
              <input type="radio" name="theme" value="dark">
              <span class="setting-label">暗色主题</span>
            </label>
            <label class="setting-option">
              <input type="radio" name="theme" value="transparent">
              <span class="setting-label">高透明度</span>
            </label>
          </div>
        </div>

        <!-- 应用设置 -->
        <div class="setting-group">
          <h4 class="setting-title">应用设置</h4>
          <div class="setting-item">
            <label class="setting-checkbox">
              <input type="checkbox" id="startup-launch">
              <span class="setting-label">开机自启动</span>
            </label>
          </div>
        </div>

        <!-- 剪贴板设置 -->
        <div class="setting-group">
          <h4 class="setting-title">剪贴板设置</h4>
          <div class="setting-item">
            <label class="setting-label">历史记录数量</label>
            <select id="history-limit" class="setting-select">
              <option value="20">20条</option>
              <option value="50" selected>50条</option>
              <option value="100">100条</option>
              <option value="200">200条</option>
            </select>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" id="settings-cancel-btn">取消</button>
        <button class="btn btn-primary" id="settings-save-btn">保存</button>
      </div>
    </div>
  </div>
</body>

</html>