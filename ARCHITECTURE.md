# QuickClipboard 架构文档

## 项目概述

QuickClipboard 是一个基于 Tauri 框架开发的跨平台剪贴板管理工具，采用 Rust 后端 + JavaScript 前端的混合架构，提供高性能的系统级功能和现代化的用户界面。

## 技术栈

### 后端 (Rust)
- **框架**: Tauri 2.0
- **语言**: Rust 2021 Edition
- **主要依赖**:
  - `tauri`: 应用框架
  - `serde`: 序列化/反序列化
  - `tokio`: 异步运行时
  - `image`: 图像处理
  - `winapi`: Windows API 集成

### 前端 (JavaScript)
- **语言**: 原生 JavaScript (ES6+)
- **样式**: CSS3
- **图标**: Tabler Icons
- **主要库**:
  - `Sortable.js`: 拖拽排序
  - Tauri API: 前后端通信

## 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    QuickClipboard 应用                      │
├─────────────────────────────────────────────────────────────┤
│  前端层 (JavaScript/HTML/CSS)                               │
│  ├── 主窗口 (index.html + main.js)                         │
│  ├── 预览窗口 (preview.html + preview.js)                  │
│  ├── 设置窗口 (settings.html + settings.js)               │
│  └── 截屏窗口 (screenshot.html + screenshot.js)           │
├─────────────────────────────────────────────────────────────┤
│  Tauri API 层                                              │
│  ├── 命令接口 (commands.rs)                                │
│  ├── 事件系统 (Tauri Events)                               │
│  └── 窗口管理 (Window Management)                          │
├─────────────────────────────────────────────────────────────┤
│  后端业务层 (Rust)                                          │
│  ├── 剪贴板管理 (clipboard_*.rs)                           │
│  ├── 设置管理 (settings.rs)                                │
│  ├── 音效管理 (sound_manager.rs)                           │
│  ├── 图片管理 (image_manager.rs)                           │
│  └── 分组管理 (groups.rs)                                  │
├─────────────────────────────────────────────────────────────┤
│  系统集成层 (Rust)                                          │
│  ├── 键盘钩子 (keyboard_hook.rs)                           │
│  ├── 系统托盘 (tray.rs)                                    │
│  ├── 窗口效果 (window_effects.rs)                          │
│  └── 截屏功能 (screenshot.rs)                              │
├─────────────────────────────────────────────────────────────┤
│  操作系统层                                                  │
│  ├── Windows API                                           │
│  ├── 文件系统                                               │
│  ├── 系统剪贴板                                             │
│  └── 音频系统                                               │
└─────────────────────────────────────────────────────────────┘
```

## 核心功能模块

### 1. 剪贴板管理系统
- **监听**: 实时监听系统剪贴板变化
- **存储**: 持久化剪贴板历史记录
- **分组**: 支持自定义分组管理
- **搜索**: 快速搜索和过滤功能

### 2. 预览窗口系统
- **快捷键**: 全局快捷键快速预览
- **滚动**: 鼠标滚轮快速切换
- **音效**: 滚动音效反馈
- **粘贴**: 自动粘贴选中内容

### 3. 设置管理系统
- **界面**: 多页面设置界面
- **实时**: 设置实时生效
- **持久化**: 设置自动保存
- **验证**: 设置项验证和错误处理

### 4. 音效系统
- **播放**: 支持多种音频格式
- **并发**: 智能并发控制
- **自定义**: 支持自定义音效文件
- **回退**: 文件播放失败时的音效回退

## 数据流向

### 剪贴板数据流
```
系统剪贴板变化 → clipboard_monitor.rs → clipboard_history.rs → 
前端事件通知 → UI更新 → 用户交互
```

### 设置数据流
```
用户设置变更 → settings.js → Tauri命令 → settings.rs → 
配置持久化 → 各模块配置更新
```

### 预览窗口数据流
```
快捷键触发 → keyboard_hook.rs → preview_window.rs → 
窗口显示 → 用户滚动 → 音效播放 → 内容粘贴
```

## 安全性设计

### 1. 内存安全
- Rust 语言的内存安全保证
- 无空指针和缓冲区溢出
- 自动内存管理

### 2. 类型安全
- 强类型系统
- 编译时错误检查
- 安全的前后端通信

### 3. 权限控制
- 最小权限原则
- 安全的文件访问
- 系统API安全调用

## 性能优化

### 1. 异步处理
- 非阻塞的剪贴板监听
- 异步文件I/O操作
- 并发音效播放

### 2. 内存管理
- 智能图片缓存
- 历史记录限制
- 及时资源释放

### 3. 启动优化
- 延迟加载非关键模块
- 快速启动机制
- 最小化启动依赖

## 扩展性设计

### 1. 模块化架构
- 清晰的模块边界
- 松耦合设计
- 可插拔组件

### 2. 配置驱动
- 功能开关配置
- 行为参数化
- 主题和样式定制

### 3. API设计
- 统一的命令接口
- 标准化的事件系统
- 版本兼容性保证

## 部署和分发

### 1. 构建系统
- Tauri 构建工具链
- 自动化构建流程
- 多平台编译支持

### 2. 打包分发
- 单文件可执行程序
- 自动更新机制
- 数字签名验证

### 3. 安装部署
- 绿色软件设计
- 用户数据迁移
- 卸载清理机制

## 开发指南

### 前端开发
- 参考 `src/js/README.md`
- 遵循模块化开发原则
- 使用统一的代码风格

### 后端开发
- 参考 `src-tauri/src/README.md`
- 遵循 Rust 最佳实践
- 注重错误处理和日志记录

### 调试和测试
- 使用 Tauri 开发工具
- 前后端分离调试
