<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文本编辑器 - 快速剪贴板</title>
  <link rel="stylesheet" href="./assets/icons/icons-webfont/dist/tabler-icons.min.css">
  <link rel="stylesheet" href="textEditor.css">
  <link rel="stylesheet" href="themes.css">
</head>

<body>
  <div class="editor-container">
    <!-- 编辑器标题栏 -->
    <div class="editor-header">
      <div class="header-content">
        <div class="header-left">
          <i class="ti ti-edit"></i>
          <h1 id="editor-title">编辑文本</h1>
        </div>
        <div class="header-controls">
          <button class="control-button minimize-btn" id="minimize-btn" title="最小化">
            <i class="ti ti-minus"></i>
          </button>
          <button class="control-button maximize-btn" id="maximize-btn" title="最大化">
            <i class="ti ti-square"></i>
          </button>
          <button class="control-button close-button" id="close-editor" title="关闭">
            <i class="ti ti-x"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑器工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <!-- 标题输入框（仅常用文本显示） -->
        <div class="title-group" id="title-group" style="display: none;">
          <label for="title-input">标题:</label>
          <input type="text" id="title-input" class="title-input" placeholder="输入标题...">
        </div>

        <!-- 分组选择（仅常用文本显示） -->
        <div class="group-selector" id="group-selector" style="display: none;">
          <label for="group-select">分组:</label>
          <select id="group-select" class="group-select">
            <option value="">选择分组</option>
          </select>
        </div>
      </div>

      <div class="toolbar-right">
        <button class="tool-btn" id="word-wrap-btn" title="切换自动换行">
          <i class="ti ti-text-wrap"></i>
        </button>
        <button class="tool-btn" id="reset-btn" title="重置为原始内容">
          <i class="ti ti-refresh"></i>
        </button>
        <span class="separator"></span>
        <span class="char-count" id="char-count">0 字符</span>
        <span class="line-count" id="line-count">1 行</span>
      </div>
    </div>

    <!-- 编辑器内容区域 -->
    <div class="editor-content">
      <textarea id="editor-textarea" class="editor-textarea" placeholder="在此编辑文本内容..." autocomplete="off"
        spellcheck="false"></textarea>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span id="status-text">就绪</span>
      </div>
      <div class="status-right">
        <button class="btn btn-secondary" id="cancel-btn">
          <i class="ti ti-x"></i>
          取消
        </button>
        <button class="btn btn-primary" id="save-btn">
          <i class="ti ti-device-floppy"></i>
          保存
        </button>
      </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
      <div class="loading-spinner">
        <i class="ti ti-loader-2"></i>
        <span>保存中...</span>
      </div>
    </div>
  </div>

  <!-- 通知容器 -->
  <div id="notification-container"></div>

  <script type="module" src="textEditor.js"></script>
</body>

</html>