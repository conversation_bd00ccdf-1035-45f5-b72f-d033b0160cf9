/* 设置页面样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
}

body {
  font-family: "Microsoft Yahei", "Microsoft Yahei", Times, serif;
  overflow: hidden;
  background: transparent;
}

.settings-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 标题栏 */
.settings-header {
  padding: 0;
  position: relative;
  z-index: 100;
  -webkit-app-region: drag;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left i {
  font-size: 24px;
  color: var(--primary-color);
}

.header-left h1 {
  font-size: 20px;
  font-weight: 600;
}

/* 窗口控制按钮容器 */
.header-controls {
  display: flex;
  align-items: center;
  gap: 4px;
  -webkit-app-region: no-drag;
  /* 禁用拖拽，允许点击 */
}

/* 通用控制按钮样式 */
.control-button {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.control-button i {
  font-size: 16px;
}

.maximize-btn {
  display: none;
}

/* 设置内容区域 */
.settings-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 侧边栏 */
.settings-sidebar {
  width: 240px;
  padding: 24px 0;
}

.settings-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.nav-item i {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* 主要设置面板 */
.settings-main {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.settings-section {
  display: none;
  max-width: 800px;
}

.settings-section.active {
  display: block;
}

.section-header {
  margin-bottom: 32px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;

}

.section-header p {
  font-size: 14px;

}

/* 设置组 */
.settings-group {
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 24px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-label {
  font-size: 16px;
  font-weight: 500;
  display: block;
  margin-bottom: 4px;

}

.setting-description {
  font-size: 14px;
  line-height: 1.4;

}

.setting-control {
  margin-left: 24px;
}

/* 开关按钮 */
.toggle-switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 48px;
  height: 28px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 28px;
  /* 添加边框确保在所有系统上都可见 */
  border: 1px solid rgba(0, 0, 0, 0.1);
  /* 确保最小可见性 */
  min-height: 28px;
  min-width: 48px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  /* 确保在所有系统上都有可见的背景 */
  border: 1px solid rgba(0, 0, 0, 0.1);
}

input:checked+.toggle-slider {
  background-color: #4a89dc;
}

input:checked+.toggle-slider:before {
  transform: translateX(20px) translateY(-50%);
}

/* 选择框 */
.setting-select {
  padding: 8px 12px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  min-width: 120px;
}

/* 主题选择器 */
.theme-selector {
  display: flex;
  gap: 16px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-preview {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.theme-auto-preview {
  background: linear-gradient(135deg, #fff 25%, #2d2d2d 25%, #2d2d2d 50%, #fff 50%, #fff 75%, #2d2d2d 75%);
  background-size: 8px 8px;
}

.theme-light-preview {
  background: linear-gradient(135deg, #fff 50%, #f8f9fa 50%);
}

.theme-dark-preview {
  background: linear-gradient(135deg, #2d2d2d 50%, #1a1a1a 50%);
}

.theme-transparent-preview {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 50%, rgba(0, 0, 0, 0.1) 50%);
}

.theme-option span {
  font-size: 12px;
  font-weight: 500;

}

/* 滑块 */
.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

#opacity-slider {
  width: 120px;
}

.slider-value {
  font-size: 14px;
  min-width: 40px;

}

/* 快捷键输入 */
.shortcut-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.shortcut-input input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
  background: #f8f9fa;
}

.shortcut-clear {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
}

.shortcut-clear:hover {
  background: #f8f9fa;
  color: #333;
}

/* 预设快捷键 */
.shortcut-presets {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.shortcut-presets .setting-description {
  margin: 0;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.preset-btn {
  padding: 4px 8px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
}

.preset-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.preset-btn:active {
  background: #dee2e6;
  transform: translateY(1px);
}

/* 关于页面 */
.about-info {
  text-align: center;
  padding: 40px 20px;
}

.app-icon {
  margin-bottom: 16px;
}

.app-icon i {
  font-size: 64px;
  color: #4a89dc;
}

.about-info h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.version {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  max-width: 400px;
  margin: 0 auto 32px;
}

.about-links {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.about-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #4a89dc;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.about-button:hover {
  background: #3a7bc8;
  transform: translateY(-1px);
}

/* 音效设置样式 */
.sound-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  max-width: 400px;
}

.sound-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.sound-input:focus {
  outline: none;
  border-color: #4a89dc;
  box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.2);
}

.sound-browse-btn,
.sound-test-btn {
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sound-browse-btn:hover,
.sound-test-btn:hover {
  background: #f8f9fa;
  border-color: #4a89dc;
  color: #4a89dc;
}

.sound-browse-btn i,
.sound-test-btn i {
  font-size: 16px;
}

.sound-test-btn.playing {
  background: #4a89dc;
  color: white;
  border-color: #4a89dc;
  cursor: not-allowed;
}

.sound-test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.sound-test-btn.playing i {
  animation: pulse 1s infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* 快捷键输入框样式 */
.shortcut-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.shortcut-input {
  flex: 1;
  padding: 8px 12px;
  /* border: 1px solid #ddd; */
  border-radius: 6px;
  font-size: 14px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shortcut-input:focus {
  outline: none;
  border-color: #4a89dc;
  background: white;
  box-shadow: 0 0 0 3px rgba(74, 137, 220, 0.1);
}

.shortcut-input.recording {
  border-color: #e74c3c;
  background: #fdf2f2;
  animation: recording-pulse 1s infinite;
}

.shortcut-clear-btn {
  padding: 8px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shortcut-clear-btn:hover {
  background: #c0392b;
}

.shortcut-clear-btn i {
  font-size: 14px;
}

@keyframes recording-pulse {

  0%,
  100% {
    border-color: #e74c3c;
  }

  50% {
    border-color: #c0392b;
  }
}

.clipboard-image {
  pointer-events: none;
}

/* 支持作者页面样式 */
.support-content {
  max-width: 800px;
  margin: 0 auto;
}

.support-message {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

.support-text {
  flex: 1;
}

.support-text h3 {
  color: #4a89dc;
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
}

.support-text p {
  margin-bottom: 16px;
  line-height: 1.6;
  color: #666;
}

.support-list {
  list-style: none;
  margin: 20px 0;
  padding: 0;
}

.support-list li {
  padding: 8px 0;
  color: #555;
  font-size: 15px;
}

.support-note {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #4a89dc;
  font-weight: 500;
  color: #4a89dc !important;
  margin-top: 20px;
}

.qr-code-container {
  display: flex;
  gap: 24px;
  flex-direction: column;
  align-items: center;
}

.qr-code-section {
  text-align: center;
}

.qr-code-section h4 {
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.qr-code {
  background: white;
  padding: 10px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
}

.qr-code:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.qr-code img {
  width: 150px;
  height: 150px;
  border-radius: 8px;
}

.qr-desc {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.github-section {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.github-section h3 {
  color: #4a89dc;
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 600;
}

.github-section p {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.6;
}

.github-link {
  display: flex;
  justify-content: center;
}

.github-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #333;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.github-btn:hover {
  background: #555;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.github-btn i {
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .support-message {
    flex-direction: column;
    padding: 24px;
  }

  .qr-code-container {
    flex-direction: row;
    justify-content: center;
    gap: 16px;
  }

  .qr-code img {
    width: 120px;
    height: 120px;
  }
}

/* 全局滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 确认对话框样式 */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.confirm-dialog {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 400px;
  max-width: 500px;
  overflow: hidden;
}

.confirm-dialog-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.confirm-dialog-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.confirm-dialog-body {
  padding: 20px 24px;
}

.confirm-dialog-body p {
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.confirm-dialog-footer {
  padding: 16px 24px 20px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-dialog-footer .btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-dialog-footer .btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.confirm-dialog-footer .btn-secondary:hover {
  background: var(--bg-hover);
}

.confirm-dialog-footer .btn-primary {
  background: var(--primary-color);
  color: white;
}

.confirm-dialog-footer .btn-primary:hover {
  background: var(--primary-hover);
}

/* AI翻译设置样式 */
.setting-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
  background: #fff;
  transition: border-color 0.2s ease;
}

.setting-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.1);
}

.setting-input[type="password"] {
  font-family: monospace;
}

.setting-textarea {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 300px;
  background: #fff;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.setting-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.1);
}

.setting-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
}

.setting-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.setting-button:active {
  background: #dee2e6;
  transform: translateY(0);
}

.setting-button i {
  font-size: 16px;
}

.setting-button-small {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
  width: 32px;
  height: 32px;
}

.setting-button-small:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.setting-button-small:active {
  background: #dee2e6;
  transform: translateY(0);
}

.setting-button-small i {
  font-size: 14px;
}

/* AI翻译输入速度滑块 */
#ai-input-speed {
  width: 150px;
}

/* 深色主题适配 */
.theme-dark .setting-input,
.theme-dark .setting-textarea {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-color);
}

.theme-dark .setting-input:focus,
.theme-dark .setting-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.2);
}

.theme-dark .setting-button {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-color);
}

.theme-dark .setting-button:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

.theme-dark .setting-button-small {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-color);
}

.theme-dark .setting-button-small:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}