/* 文本编辑器样式 */

/* 主题变量 */
:root {
  /* 浅色主题 */
  --bg-color: #ffffff;
  --text-color: #333333;
  --text-muted: #555555;
  --border-color: #e9ecef;
  --toolbar-bg: #f8f9fa;
  --input-bg: #ffffff;
  --header-bg: #ffffff;
  --primary-color: #108FEB;
  --hover-bg: #f1f3f4;
  --button-bg: #ffffff;
  --button-border: #d0d7de;
  --button-hover-bg: #f3f4f6;
  --button-primary-bg: #108FEB;
  --button-primary-hover: #0969da;
  --shadow: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
  /* 深色主题 */
  --bg-color: #1a1a1a;
  --text-color: #e6e6e6;
  --text-muted: #a0a0a0;
  --border-color: #404040;
  --toolbar-bg: #2a2a2a;
  --input-bg: #2a2a2a;
  --header-bg: #2a2a2a;
  --primary-color: #4dabf7;
  --hover-bg: #3a3a3a;
  --button-bg: #2a2a2a;
  --button-border: #404040;
  --button-hover-bg: #3a3a3a;
  --button-primary-bg: #4dabf7;
  --button-primary-hover: #339af0;
  --shadow: rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  background: var(--bg-color);
  /* color: var(--text-color); */
  overflow: hidden;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.editor-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-color, #ffffff);
  min-width: 400px;
  min-height: 300px;
}

/* 标题栏样式 */
.editor-header {
  background: var(--header-bg, #ffffff);
  border-bottom: 1px solid var(--border-color, #e9ecef);
  padding: 0;
  user-select: none;
  -webkit-app-region: drag;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  padding: 0 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left i {
  font-size: 18px;
  color: var(--primary-color, #108FEB);
}

.header-left h1 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333);
}

.header-controls {
  display: flex;
  gap: 4px;
  -webkit-app-region: no-drag;
}

.control-button {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-color, #666);
}

.control-button:hover {
  background: var(--hover-bg, #f1f3f4);
}

.maximize-btn:hover {
  background: var(--hover-bg);
}

.close-button:hover {
  background: #ff4757;
  color: white;
}

/* 编辑器工具栏 */
.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--toolbar-bg, #f8f9fa);
  border-bottom: 1px solid var(--border-color, #e9ecef);
  min-height: 48px;
  flex-shrink: 0;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 标题输入框 */
.title-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-group label {
  font-size: 14px;
  color: #222222;
  white-space: nowrap;
  font-weight: 500;
}

[data-theme="dark"] .title-group label {
  color: #e6e6e6;
}

.title-input {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  width: 220px;
  background: var(--input-bg);
  color: #222222;
  transition: all 0.2s ease;
}

[data-theme="dark"] .title-input {
  color: #e6e6e6;
}

.title-input:focus {
  outline: none;
  border-color: var(--primary-color, #108FEB);
  box-shadow: 0 0 0 3px rgba(16, 143, 235, 0.1);
}

/* 分组选择器 */
.group-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-selector label {
  font-size: 14px;
  color: #222222;
  white-space: nowrap;
  font-weight: 500;
}

[data-theme="dark"] .group-selector label {
  color: #e6e6e6;
}

.group-select {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  width: 160px;
  background: var(--input-bg) !important;
  color: #222222 !important;
  transition: all 0.2s ease;
}

[data-theme="dark"] .group-select {
  color: #e6e6e6 !important;
}

.group-select:focus {
  outline: none;
  border-color: var(--primary-color, #108FEB);
  box-shadow: 0 0 0 3px rgba(16, 143, 235, 0.1);
}

/* 内容区域 */
.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 表单区块 */
.form-section {
  background: var(--card-bg, #ffffff);
  border: 1px solid var(--border-color, #e9ecef);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color, #e9ecef);
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #333);
}

.section-header i {
  font-size: 18px;
  color: var(--primary-color, #108FEB);
}

.section-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid var(--border-color, #e9ecef);
  background: var(--bg-color, #ffffff);
  border-radius: 6px;
  font-size: 13px;
  color: var(--text-color, #666);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--hover-bg, #f8f9fa);
  border-color: var(--primary-color, #108FEB);
  color: var(--primary-color, #108FEB);
}

/* 原始内容显示 */
.original-content {
  background: var(--code-bg, #f8f9fa);
  border: 1px solid var(--border-color, #e9ecef);
  border-radius: 8px;
  padding: 16px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: var(--text-color, #333);
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
}

/* 表单组 */
.form-group {
  margin-bottom: 16px;
}

/* 编辑器文本区域 */
.editor-textarea {
  width: 100%;
  height: 100%;
  padding: 16px;
  border: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-color, #333);
  background: var(--input-bg, #ffffff);
  resize: none;
  outline: none;
  overflow-y: auto;
  white-space: pre;
  word-wrap: normal;
}

.editor-textarea.word-wrap {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--toolbar-bg, #f8f9fa);
  border-top: 1px solid var(--border-color, #e9ecef);
  min-height: 48px;
  flex-shrink: 0;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 13px;
  color: var(--text-muted, #666);
}

.status-right {
  display: flex;
  gap: 12px;
}

.tool-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-color, #666);
}

.tool-btn:hover {
  background: var(--hover-bg, #f1f3f4);
  color: var(--primary-color, #108FEB);
}

.tool-btn.active {
  background: var(--primary-color, #108FEB);
  color: white;
}

/* 分隔符 */
.separator {
  width: 1px;
  height: 20px;
  background: var(--border-color, #e9ecef);
  margin: 0 8px;
}

/* 字符和行数统计 */
.char-count,
.line-count {
  font-size: 12px;
  color: var(--text-muted, #666);
  white-space: nowrap;
}

/* 操作按钮 */
.editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color, #e9ecef);
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-secondary {
  background: var(--button-bg);
  color: var(--text-color);
  border: 1px solid var(--button-border);
}

.btn-secondary:hover {
  background: var(--button-hover-bg);
}

.btn-primary {
  background: var(--button-primary-bg);
  color: white;
  border: 1px solid var(--button-primary-bg);
}

.btn-primary:hover {
  background: var(--button-primary-hover);
  border-color: var(--button-primary-hover);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.loading-spinner {
  background: var(--card-bg, #ffffff);
  padding: 24px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-spinner i {
  font-size: 24px;
  color: var(--primary-color, #108FEB);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 通知容器 */
#notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10001;
}

/* 响应式设计 */
@media (max-width: 768px) {
  /* .editor-content {
    padding: 16px;
  } */

  .editor-form {
    gap: 16px;
  }

  .form-section {
    padding: 16px;
  }

  .editor-textarea {
    min-height: 250px;
  }
}

/* 全局滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}