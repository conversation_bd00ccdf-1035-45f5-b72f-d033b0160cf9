* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
  font-family: "Microsoft Yahei", "Microsoft Yahei", Times, serif;
}

html,
body {
  height: 100%;
  background: transparent !important;
  overflow: hidden;
  border: none !important;
  outline: none !important;
  /* margin: 1px !important; */
  padding: 0 !important;
}

.preview-container {
  /* height: 100vh; */
  /* width: 100vw; */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.preview-status {
  /* position: absolute;
  top: 8px;
  right: 8px; */
  /* margin-bottom: 16px; */
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1px 8px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
  width: fit-content;
  text-align: center;
  margin: auto;
  /* margin-bottom: 2px; */
}

.preview-list {
  flex: 1;
  overflow: hidden;
  padding: 8px;
  padding-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
  max-height: fit-content;
  /* border: 2px solid rgba(255, 255, 255, 0.8); */
  /* border-radius: 8px; */
}

/* 预览项位置样式 */
.preview-item.prev,
.preview-item.next {
  opacity: 0.8;
  transform: scale(0.95);
  /* filter: blur(1px); */
}

.preview-item.current {
  opacity: 1;
  transform: scale(1);
  filter: none;
}

.preview-item.placeholder {
  opacity: 0;
  pointer-events: none;
}

.preview-item {
  background: rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  padding: 8px 12px;
  color: #e0e0e0;
  font-size: 12px;
  line-height: 1.4;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 32px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* .preview-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
} */

.preview-item.active {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(74, 137, 220, 1);
  color: #ffffff;
  box-shadow: 0 0 12px rgba(74, 137, 220, 0.5);
  transform: scale(1.05);
  z-index: 2;
  padding: 4px 12px;
  font-weight: bold;
  font-size: 1em;
}

.preview-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #4a89dc;
}

/* 文本内容样式 */
.preview-text {
  flex: 1;
  word-break: break-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 图片内容样式 */
.preview-image {
  max-width: 40px;
  max-height: 24px;
  border-radius: 3px;
  object-fit: contain;
  margin-right: 8px;
  flex-shrink: 0;
}

.preview-image-text {
  flex: 1;
  color: #bbb;
  font-style: italic;
}

/* 链接内容样式 */
.preview-link {
  color: #7db8ff;
}

.preview-link::before {
  content: '🔗 ';
  margin-right: 4px;
}

/* 内容类型指示器 */
.content-type-indicator {
  position: absolute;
  top: 4px;
  right: 6px;
  font-size: 10px;
  color: rgba(255, 255, 255, 1);
  background: rgba(255, 255, 255, 0.5);
  padding: 1px 4px;
  border-radius: 2px;
  font-weight: 500;
}

/* 滚动动画 */
.preview-list {
  transition: transform 0.2s ease-out;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.6;
}