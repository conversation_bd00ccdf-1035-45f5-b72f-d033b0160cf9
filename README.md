# 快速剪贴板 (QuickClipboard)

一个简洁高效的剪贴板历史管理工具，让复制粘贴更便捷！

## ✨ 核心特色

### 🚀 一键操作，极速便捷
- **`Win+V`** - 瞬间显示剪贴板历史
- **`Ctrl+~`** - 快速预览窗口，滚动选择即粘贴
- **`Ctrl+1-9`** - 数字键快速粘贴对应历史记录
- **`Ctrl+Shift+A`** - 一键截屏并自动保存到剪贴板

### 📋 智能剪贴板管理
- **自动记录**：复制任何内容自动保存历史（文本/图片/链接）
- **实时搜索**：输入关键词瞬间找到历史内容
- **去重过滤**：自动忽略重复内容，保持列表整洁
- **分类筛选**：按文本、图片、链接类型快速筛选

### ⭐ 常用文本收藏
- **永久保存**：重要内容添加到收藏夹，永不丢失
- **分组管理**：按项目分类，工作生活井然有序
- **拖拽排序**：随意调整顺序，常用的放前面
- **快速编辑**：随时修改标题和内容

### 🎨 个性化体验
- **多主题**：浅色/暗色/跟随系统，护眼舒适
- **音效反馈**：复制粘贴有声提示，操作更有趣
- **后台运行**：最小化到托盘，不占任务栏空间
- **开机自启**：开机即用，无需手动启动

## 🚀 快速开始

1. **下载安装**：从 [Releases](https://github.com/mosheng1/QuickClipboard/releases) 下载最新版本
2. **启动应用**：运行后自动最小化到系统托盘
3. **开始使用**：按 `Win+V` 显示窗口，复制任何内容即可自动记录

## 💡 使用技巧

### 🔥 高效工作流
- **预览窗口**：`Ctrl+~` 快速浏览，无需打开主窗口
- **数字快捷键**：`Ctrl+1-9` 瞬间粘贴最近9条记录
- **分组收藏**：常用内容按项目分组，提高查找效率
- **搜索定位**：关键词搜索，快速找到历史内容

### ⚠️ 注意事项
- 需要剪贴板和屏幕录制权限
- 某些安全软件可能拦截全局快捷键
- 图片内容占用存储空间较大

## 🛠️ 开发构建

### 环境要求
- Node.js >= 16.0.0
- Rust >= 1.70.0
- Tauri CLI >= 2.0.0

### 开发命令
```bash
# 安装依赖
npm install

# 开发模式
npm run tauri dev

# 构建应用
npm run tauri build
```

### 技术栈
- **前端**：JavaScript + HTML5 + CSS3
- **后端**：Rust + Tauri 2.0
- **构建**：Vite + Tauri CLI

## 🙏 致谢

感谢以下开源项目：
- [Tauri](https://tauri.app/) - 跨平台应用框架
- [Tabler Icons](https://tabler-icons.io/) - 图标库
- [SortableJS](https://sortablejs.github.io/Sortable/) - 拖拽排序

## 📄 许可证

Apache-2.0 License - 查看 [LICENSE](LICENSE) 文件了解详情

---

**快速剪贴板 - 让复制粘贴更高效！** 🚀
