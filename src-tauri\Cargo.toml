[package]
name = "QuickClipboard"
version = "0.0.1"
description = "一个功能强大的剪贴板管理工具，帮助您更高效地管理和使用剪贴板内容"
authors = ["MoSheng"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "quickclipboard_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["tray-icon"] }
tauri-plugin-opener = "2"
tauri-plugin-global-shortcut = "2"
tauri-plugin-notification = "2"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
arboard = "3.3.0"
once_cell = "1.18.0"
enigo = "0.2.0"
image = "0.24"
base64 = "0.21"
uuid = { version = "1.0", features = ["v4"] }
chrono = "0.4"
auto-launch = "0.5"
dirs = "5.0"
sha2 = "0.10"
rodio = "0.17"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream"] }
rfd = "0.14"
screenshots = "0.8"
thiserror = "2.0"
futures-util = "0.3"
fastrand = "2.0"
regex = "1.11.1"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.56.0", features = [
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Foundation",
    "Win32_System_LibraryLoader",
    "Win32_System_DataExchange",
    "Win32_System_Memory",
    "Win32_Graphics_Gdi",
    "Win32_Graphics_Dwm",
    "Win32_Security",
    "Win32_System_Threading",
    "Win32_UI_Shell",
    "Win32_System_Ole",
    "Win32_Storage_FileSystem"
] }
window-vibrancy = "0.6"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

